<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreSupportTicketRequest;
use App\Http\Requests\UpdateSupportTicketRequest;
use App\Mail\NewSupportTicketMail;
use App\Models\AdminNotifiable;
use App\Models\SupportTicket;
use App\Models\User;
use App\Notifications\NewSupportTicketNotification;
use App\Tenant\Manager;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification;
use Carbon\Carbon;

class SupportTicketController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the resource for users.
     */
    public function index(): View
    {
        $this->authorize('viewAny', SupportTicket::class);
        $company = app(Manager::class)->getTenant();
        return view('support-tickets.index', compact('company'));
    }

    /**
     * Display admin view of all tickets.
     */
    public function adminIndex(): View
    {
        $this->authorize('manage', SupportTicket::class);
        return view('support-tickets.admin.index');
    }

    /**
     * Get tickets data for DataTables (user view).
     */
    public function getUserTickets(Request $request): JsonResponse
    {
        $query = SupportTicket::with(['user', 'assignedTo'])
            ->where('user_id', Auth::id())
            ->orderBy('created_at', 'desc');

        return datatables()->of($query)
            ->editColumn('status', function (SupportTicket $ticket) {
                $statusLabels = SupportTicket::getStatuses();
                return '<span class="badge ' . $ticket->status_badge_class . '">' . $statusLabels[$ticket->status] . '</span>';
            })
            ->editColumn('priority', function (SupportTicket $ticket) {
                $priorityLabels = SupportTicket::getPriorities();
                return '<span class="badge ' . $ticket->priority_badge_class . '">' . $priorityLabels[$ticket->priority] . '</span>';
            })
            ->editColumn('created_at', function (SupportTicket $ticket) {
                return $ticket->created_at->format('M d, Y h:i A') . '<br><small class="text-muted">' . $ticket->created_at->diffForHumans() . '</small>';
            })
            ->addColumn('action', function (SupportTicket $ticket) {
                $actions = '<a href="' . route('account.support-tickets.show', $ticket->id) . '" class="btn btn-sm btn-light-primary">View</a>';
                if ($ticket->isOpen()) {
                    $actions .= ' <a href="' . route('account.support-tickets.edit', $ticket->id) . '" class="btn btn-sm btn-light-warning">Edit</a>';
                }
                return $actions;
            })
            ->rawColumns(['status', 'priority', 'created_at', 'action'])
            ->toJson();
    }

    /**
     * Get all tickets data for DataTables (admin view).
     */
    public function getAdminTickets(Request $request): JsonResponse
    {
        // Only admins can access this
        if (!Auth::user()->hasAnyRole(['super-admin', 'master-reseller'])) {
            abort(403, 'Unauthorized access.');
        }

        $query = SupportTicket::with(['user', 'assignedTo'])
            ->orderBy('created_at', 'desc');

        return datatables()->of($query)
            ->addColumn('user_name', function (SupportTicket $ticket) {
                return $ticket->user->name . '<br><small class="text-muted">' . $ticket->user->email . '</small>';
            })
            ->editColumn('status', function (SupportTicket $ticket) {
                $statusLabels = SupportTicket::getStatuses();
                return '<span class="badge ' . $ticket->status_badge_class . '">' . $statusLabels[$ticket->status] . '</span>';
            })
            ->editColumn('priority', function (SupportTicket $ticket) {
                $priorityLabels = SupportTicket::getPriorities();
                return '<span class="badge ' . $ticket->priority_badge_class . '">' . $priorityLabels[$ticket->priority] . '</span>';
            })
            ->addColumn('assigned_to_name', function (SupportTicket $ticket) {
                return $ticket->assignedTo ? $ticket->assignedTo->name : '<span class="text-muted">Unassigned</span>';
            })
            ->editColumn('created_at', function (SupportTicket $ticket) {
                return $ticket->created_at->format('M d, Y h:i A') . '<br><small class="text-muted">' . $ticket->created_at->diffForHumans() . '</small>';
            })
            ->addColumn('action', function (SupportTicket $ticket) {
                $actions = '<a href="' . route('admin.support-tickets.show', $ticket->id) . '" class="btn btn-sm btn-light-primary">View</a>';
                $actions .= ' <a href="' . route('admin.support-tickets.edit', $ticket->id) . '" class="btn btn-sm btn-light-warning">Manage</a>';
                return $actions;
            })
            ->rawColumns(['user_name', 'status', 'priority', 'assigned_to_name', 'created_at', 'action'])
            ->toJson();
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(): View
    {
        $this->authorize('create', SupportTicket::class);
        $company = app(Manager::class)->getTenant();
        return view('support-tickets.create', compact('company'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreSupportTicketRequest $request): RedirectResponse
    {
        $supportTicket = SupportTicket::create([
            'user_id' => Auth::id(),
            'subject' => $request->subject,
            'description' => $request->description,
            'priority' => $request->priority,
            'status' => SupportTicket::STATUS_OPEN,
        ]);

        // Send email notification to admin
        try {
            $adminEmail = config('mail.admin_email', env('ADMIN_EMAIL', '<EMAIL>'));
            Mail::to($adminEmail)->send(new NewSupportTicketMail($supportTicket));
        } catch (\Exception $e) {
            // Log the error but don't fail the ticket creation
            \Log::error('Failed to send support ticket notification email: ' . $e->getMessage());
        }

        return redirect()->route('account.support-tickets')
            ->with('success', 'Support ticket created successfully. We will get back to you soon.');
    }

    /**
     * Display the specified resource.
     */
    public function show(SupportTicket $supportTicket): View
    {
        $this->authorize('view', $supportTicket);
        $company = app(Manager::class)->getTenant();
        return view('support-tickets.show', compact('supportTicket', 'company'));
    }

    /**
     * Display the specified resource for admin.
     */
    public function adminShow(SupportTicket $supportTicket): View
    {
        // Only admins can access this
        if (!Auth::user()->hasAnyRole(['super-admin', 'master-reseller'])) {
            abort(403, 'Unauthorized access.');
        }

        $adminUsers = User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['super-admin', 'master-reseller']);
        })->get();

        return view('support-tickets.admin.show', compact('supportTicket', 'adminUsers'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(SupportTicket $supportTicket): View
    {
        // Users can only edit their own open tickets
        if ($supportTicket->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access.');
        }

        if (!$supportTicket->isOpen()) {
            return redirect()->route('account.support-tickets.show', $supportTicket)
                ->with('error', 'You can only edit open tickets.');
        }

        $company = app(Manager::class)->getTenant();
        return view('support-tickets.edit', compact('supportTicket', 'company'));
    }

    /**
     * Show the form for editing the specified resource (admin).
     */
    public function adminEdit(SupportTicket $supportTicket): View
    {
        // Only admins can access this
        if (!Auth::user()->hasAnyRole(['super-admin', 'master-reseller'])) {
            abort(403, 'Unauthorized access.');
        }

        $adminUsers = User::whereHas('roles', function ($query) {
            $query->whereIn('name', ['super-admin', 'master-reseller']);
        })->get();

        return view('support-tickets.admin.edit', compact('supportTicket', 'adminUsers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateSupportTicketRequest $request, SupportTicket $supportTicket): RedirectResponse
    {

        $supportTicket->update([
            'subject' => $request->subject,
            'description' => $request->description,
            'priority' => $request->priority,
        ]);

        return redirect()->route('account.support-tickets.show', $supportTicket)
            ->with('success', 'Support ticket updated successfully.');
    }

    /**
     * Update the specified resource in storage (admin).
     */
    public function adminUpdate(UpdateSupportTicketRequest $request, SupportTicket $supportTicket): RedirectResponse
    {

        $updateData = [
            'status' => $request->status,
            'priority' => $request->priority,
            'assigned_to' => $request->assigned_to,
            'admin_notes' => $request->admin_notes,
        ];

        // Set resolved_at timestamp if status is being changed to resolved
        if ($request->status === SupportTicket::STATUS_RESOLVED && $supportTicket->status !== SupportTicket::STATUS_RESOLVED) {
            $updateData['resolved_at'] = now();
        }

        $supportTicket->update($updateData);

        return redirect()->route('admin.support-tickets.show', $supportTicket)
            ->with('success', 'Support ticket updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SupportTicket $supportTicket): RedirectResponse
    {
        // Only admins can delete tickets
        if (!Auth::user()->hasAnyRole(['super-admin'])) {
            abort(403, 'Unauthorized access.');
        }

        $supportTicket->delete();

        return redirect()->route('admin.support-tickets.index')
            ->with('success', 'Support ticket deleted successfully.');
    }

    /**
     * Get ticket statistics for dashboard
     */
    public function getStatistics(): JsonResponse
    {
        if (!Auth::user()->hasAnyRole(['super-admin', 'master-reseller'])) {
            abort(403, 'Unauthorized access.');
        }

        $stats = [
            'total' => SupportTicket::count(),
            'open' => SupportTicket::where('status', SupportTicket::STATUS_OPEN)->count(),
            'in_progress' => SupportTicket::where('status', SupportTicket::STATUS_IN_PROGRESS)->count(),
            'resolved' => SupportTicket::where('status', SupportTicket::STATUS_RESOLVED)->count(),
            'closed' => SupportTicket::where('status', SupportTicket::STATUS_CLOSED)->count(),
            'critical' => SupportTicket::where('priority', SupportTicket::PRIORITY_CRITICAL)->count(),
            'high' => SupportTicket::where('priority', SupportTicket::PRIORITY_HIGH)->count(),
        ];

        return response()->json($stats);
    }
}
