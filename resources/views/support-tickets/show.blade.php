@extends('layouts.app')

@section('content')
    @php
        $user = Auth::user();
        $statusLabels = \App\Models\SupportTicket::getStatuses();
        $priorityLabels = \App\Models\SupportTicket::getPriorities();
    @endphp
    <!--begin::Container-->
    <div class="container-xxl" id="kt_content_container">
        @include('account.navbar')
        <!--begin::details View-->
        <div class="card mb-5 mb-xl-10">
            <!--begin::Card header-->
            <div class="card-header cursor-pointer">
                <!--begin::Card title-->
                <div class="card-title m-0">
                    <h3 class="fw-bold m-0">Support Ticket #{{ $supportTicket->id }}</h3>
                </div>
                <!--end::Card title-->
                <!--begin::Action-->
                <div class="d-flex align-items-center">
                    @if($supportTicket->isOpen())
                        <a href="{{route('account.support-tickets.edit', $supportTicket)}}" class="btn btn-sm btn-warning me-2">
                            <i class="ki-duotone ki-pencil fs-2"></i>
                            Edit Ticket
                        </a>
                    @endif
                    <a href="{{route('account.support-tickets')}}" class="btn btn-sm btn-light">
                        <i class="ki-duotone ki-arrow-left fs-2"></i>
                        Back to Tickets
                    </a>
                </div>
                <!--end::Action-->
            </div>
            <!--begin::Card header-->
            <!--begin::Card body-->
            <div class="card-body p-9">
                <!--begin::Row-->
                <div class="row mb-7">
                    <!--begin::Col-->
                    <div class="col-lg-6">
                        <!--begin::Details-->
                        <div class="d-flex flex-column">
                            <div class="fs-6 fw-bold text-gray-800 mb-2">{{ $supportTicket->subject }}</div>
                            <div class="d-flex align-items-center mb-2">
                                <span class="badge {{ $supportTicket->status_badge_class }} me-2">{{ $statusLabels[$supportTicket->status] }}</span>
                                <span class="badge {{ $supportTicket->priority_badge_class }}">{{ $priorityLabels[$supportTicket->priority] }}</span>
                            </div>
                            <div class="text-muted fs-7">
                                Created: {{ $supportTicket->created_at->format('M d, Y h:i A') }} ({{ $supportTicket->created_at->diffForHumans() }})
                            </div>
                            @if($supportTicket->resolved_at)
                                <div class="text-muted fs-7">
                                    Resolved: {{ $supportTicket->resolved_at->format('M d, Y h:i A') }} ({{ $supportTicket->resolved_at->diffForHumans() }})
                                </div>
                            @endif
                        </div>
                        <!--end::Details-->
                    </div>
                    <!--end::Col-->
                    <!--begin::Col-->
                    <div class="col-lg-6">
                        <!--begin::Stats-->
                        <div class="d-flex flex-column">
                            @if($supportTicket->assignedTo)
                                <div class="d-flex align-items-center mb-2">
                                    <span class="text-muted me-2">Assigned to:</span>
                                    <span class="fw-bold">{{ $supportTicket->assignedTo->name }}</span>
                                </div>
                            @endif
                            <div class="d-flex align-items-center">
                                <span class="text-muted me-2">Last updated:</span>
                                <span class="fw-bold">{{ $supportTicket->updated_at->format('M d, Y h:i A') }}</span>
                            </div>
                        </div>
                        <!--end::Stats-->
                    </div>
                    <!--end::Col-->
                </div>
                <!--end::Row-->

                <!--begin::Separator-->
                <div class="separator separator-dashed my-6"></div>
                <!--end::Separator-->

                <!--begin::Description-->
                <div class="mb-7">
                    <h5 class="fw-bold text-gray-800 mb-4">Description</h5>
                    <div class="p-6 bg-light-primary rounded">
                        <div class="text-gray-800 fs-6" style="white-space: pre-wrap;">{{ $supportTicket->description }}</div>
                    </div>
                </div>
                <!--end::Description-->

                @if($supportTicket->admin_notes)
                    <!--begin::Admin Notes-->
                    <div class="mb-7">
                        <h5 class="fw-bold text-gray-800 mb-4">Admin Response</h5>
                        <div class="p-6 bg-light-success rounded">
                            <div class="text-gray-800 fs-6" style="white-space: pre-wrap;">{{ $supportTicket->admin_notes }}</div>
                        </div>
                    </div>
                    <!--end::Admin Notes-->
                @endif

                <!--begin::Status Timeline-->
                <div class="mb-7">
                    <h5 class="fw-bold text-gray-800 mb-4">Status Timeline</h5>
                    <div class="timeline">
                        <!--begin::Timeline item-->
                        <div class="timeline-item">
                            <div class="timeline-line w-40px"></div>
                            <div class="timeline-icon symbol symbol-circle symbol-40px">
                                <div class="symbol-label bg-light-primary">
                                    <i class="ki-duotone ki-plus fs-2 text-primary"></i>
                                </div>
                            </div>
                            <div class="timeline-content mb-10 mt-n1">
                                <div class="pe-3 mb-5">
                                    <div class="fs-5 fw-semibold mb-2">Ticket Created</div>
                                    <div class="d-flex align-items-center mt-1 fs-6">
                                        <div class="text-muted me-2 fs-7">{{ $supportTicket->created_at->format('M d, Y h:i A') }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--end::Timeline item-->

                        @if($supportTicket->status !== \App\Models\SupportTicket::STATUS_OPEN)
                            <!--begin::Timeline item-->
                            <div class="timeline-item">
                                <div class="timeline-line w-40px"></div>
                                <div class="timeline-icon symbol symbol-circle symbol-40px">
                                    <div class="symbol-label bg-light-warning">
                                        <i class="ki-duotone ki-abstract-26 fs-2 text-warning"></i>
                                    </div>
                                </div>
                                <div class="timeline-content mb-10 mt-n1">
                                    <div class="pe-3 mb-5">
                                        <div class="fs-5 fw-semibold mb-2">Status Updated</div>
                                        <div class="d-flex align-items-center mt-1 fs-6">
                                            <div class="text-muted me-2 fs-7">Status changed to: <span class="badge {{ $supportTicket->status_badge_class }}">{{ $statusLabels[$supportTicket->status] }}</span></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--end::Timeline item-->
                        @endif

                        @if($supportTicket->resolved_at)
                            <!--begin::Timeline item-->
                            <div class="timeline-item">
                                <div class="timeline-line w-40px"></div>
                                <div class="timeline-icon symbol symbol-circle symbol-40px">
                                    <div class="symbol-label bg-light-success">
                                        <i class="ki-duotone ki-check fs-2 text-success"></i>
                                    </div>
                                </div>
                                <div class="timeline-content mb-10 mt-n1">
                                    <div class="pe-3 mb-5">
                                        <div class="fs-5 fw-semibold mb-2">Ticket Resolved</div>
                                        <div class="d-flex align-items-center mt-1 fs-6">
                                            <div class="text-muted me-2 fs-7">{{ $supportTicket->resolved_at->format('M d, Y h:i A') }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!--end::Timeline item-->
                        @endif
                    </div>
                </div>
                <!--end::Status Timeline-->
            </div>
            <!--end::Card body-->
        </div>
        <!--end::details View-->
    </div>
    <!--end::Container-->
@endsection
